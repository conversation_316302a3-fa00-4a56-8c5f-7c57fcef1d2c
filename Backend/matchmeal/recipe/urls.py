
from django.urls import path
from .views import *

urlpatterns = [
    path('create-recipe/', CreateMealDBRecette.as_view(), name='create_recipe'),
    path('get-recipe/', GetRecipe.as_view(), name='get_recipe'),

    path('like-recipe/', LikeRecipe.as_view(), name='like_recipe'),
    # path('unlike-recipe/', UnlikeRecipe.as_view(), name='unlike_recipe'),

    path('get-matched-recipes/', GetMatchedRecipes.as_view(), name='get_matched_recipes'),

    path('get-recipe-details/<int:recipe_id>/', GetRecipeDetails.as_view(), name='get_recipe_details'),
]
