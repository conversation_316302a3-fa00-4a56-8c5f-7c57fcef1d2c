import React from "react";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";

const ProfileScreen = ({ navigation }: any) => {
  const user = {
    name: "<PERSON>",
    age: 27,
    bio: "Amoureux de cuisine italienne 🍕 | Fan de découvertes culinaires ✨",
    avatar: "https://i.pravatar.cc/400",
    stats: {
      favorites: 14,
      myRecipes: 6,
      countries: 9,
    },
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={{ padding: 20 }}>
        {/* Header */}
        <Text style={styles.header}>Mon profil</Text>

        {/* Avatar + Infos */}
        <View style={styles.profileSection}>
          <Image source={{ uri: user.avatar }} style={styles.avatar} />
          <Text style={styles.name}>
            {user.name}, {user.age}
          </Text>
          <Text style={styles.bio}>{user.bio}</Text>
        </View>

        {/* Widgets */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Ionicons name="heart" size={22} color="#FF6B6B" />
            <Text style={styles.statNumber}>{user.stats.favorites}</Text>
            <Text style={styles.statLabel}>Favoris</Text>
          </View>

          <View style={styles.statCard}>
            <Ionicons name="book" size={22} color="#444" />
            <Text style={styles.statNumber}>{user.stats.myRecipes}</Text>
            <Text style={styles.statLabel}>Recettes</Text>
          </View>

          <View style={styles.statCard}>
            <Ionicons name="globe-outline" size={22} color="#3B82F6" />
            <Text style={styles.statNumber}>{user.stats.countries}</Text>
            <Text style={styles.statLabel}>Pays goûtés</Text>
          </View>
        </View>

        {/* Options */}
        <View style={styles.options}>

          <TouchableOpacity
            style={styles.optionButton}
            onPress={() => navigation.navigate("Settings")}
          >
            <Ionicons name="settings-outline" size={22} color="#444" />
            <Text style={styles.optionText}>Paramètres</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  header: { fontSize: 24, fontWeight: "700", color: "#222", marginBottom: 20 },
  profileSection: { alignItems: "center", marginBottom: 20 },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 2,
    borderColor: "#FF6B6B",
  },
  name: { fontSize: 20, fontWeight: "700", marginTop: 12, color: "#333" },
  bio: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
    marginTop: 6,
    paddingHorizontal: 40,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 30,
    marginTop: 10,
  },
  statCard: {
    flex: 1,
    alignItems: "center",
    backgroundColor: "#f9f9f9",
    paddingVertical: 16,
    marginHorizontal: 6,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  statNumber: { fontSize: 18, fontWeight: "700", color: "#222", marginTop: 6 },
  statLabel: { fontSize: 13, color: "#666", marginTop: 2 },
  options: { marginTop: 10 },
  optionButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  optionText: { fontSize: 16, marginLeft: 12, color: "#333", fontWeight: "500" },
});

export default ProfileScreen;
