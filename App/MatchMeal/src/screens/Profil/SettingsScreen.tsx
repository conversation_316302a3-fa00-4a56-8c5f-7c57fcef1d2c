
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { AuthContext } from '../../context/AuthContext';
import { MaterialCommunityIcons } from '@expo/vector-icons';


const SettingsScreen = () => {
  const navigation = useNavigation();
  const [isNotificationsEnabled, setIsNotificationsEnabled] = useState(true);

  const { logout } = React.useContext(AuthContext);

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          onPress: () => {
            // Perform logout logic here
            logout();
          },
        },
      ],
      { cancelable: false }
    );
  };

  return (
    <SafeAreaView style={styles.container}>
        <ScrollView style={styles.container}>
        <View style={styles.header}>
            <Text style={styles.headerText}>Settings</Text>
        </View>
        <View style={styles.content}>
            <View style={styles.contentRow}>
                <Text>Notifications</Text>
                <TouchableOpacity onPress={() => setIsNotificationsEnabled(!isNotificationsEnabled)}>
                    <MaterialCommunityIcons
                        name={isNotificationsEnabled ? 'checkbox-marked' : 'checkbox-blank-outline'}
                        size={24}
                        color={isNotificationsEnabled ? '#000' : '#ccc'}
                    />
                </TouchableOpacity>

            </View>
            <View style={styles.contentRow}>
                <Text>Version</Text>
                <Text>1.0.0</Text>
            </View>
        </View>
        </ScrollView>

    </SafeAreaView>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#fff',
    },
    header: {
      height: 50,
      backgroundColor: '#fff',
      alignItems: 'center',
      justifyContent: 'center',
    },
    headerText: {
      fontSize: 20,
      fontWeight: 'bold',
    },
    content: {
      flex: 1,
      padding: 20,
    },
    contentRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
    },
  });

  export default SettingsScreen;
  