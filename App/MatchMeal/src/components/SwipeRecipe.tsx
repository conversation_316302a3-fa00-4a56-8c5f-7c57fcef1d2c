import React, { useEffect, useState, useRef } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  Dimensions,
  Alert,
  Animated,
  Vibration,
  Modal,
  TouchableOpacity,
  Button,
} from "react-native";
import Swiper from "react-native-deck-swiper";
import { MaterialIcons } from '@expo/vector-icons'; // Icônes
import api from "../api/api";
import * as Haptics from 'expo-haptics';

interface Ingredient {
    name: string;
    quantity: string;
}

interface Recipe {
    id: string;
  nom: string;
  categorie: string;
  origine: string;
  instructions: string;
  image: string;
  rarety: string;
  ingredients: Ingredient[];
}

const rarityStyles: { [key: string]: any } = {
  commun: { color: "#A0A0A0", shadow: 5, scale: 1 },
  rare: { color: "#3B82F6", shadow: 8, scale: 1.02 },
  legendaire: { color: "#9333EA", shadow: 12, scale: 1.05 },
  unique: { color: "#FACC15", shadow: 16, scale: 1.08 },
};


interface RecipeCardProps {
  card: Recipe;
  isActive: boolean;
  swipeAnim: Animated.Value;
  setIsAnimating: (isAnimating: boolean) => void;
  currentCard: number;
}


const RecipeCard: React.FC<RecipeCardProps> = ({ card, isActive, swipeAnim, setIsAnimating, currentCard }) => {
    const style = rarityStyles[card.rarety] || rarityStyles.commun;
    console.log("card : ", card.rarety, style);
    
    // Animation de la carte en scale
    const scaleAnim = useRef(new Animated.Value(isActive ? 1 : 0.8)).current; // valeur initiale
    

    useEffect(() => {
        if (isActive) {
            setIsAnimating(true); // bloque le swipe
            Animated.spring(scaleAnim, {
                toValue: 1,
                friction: 6,
                tension: 80,
                useNativeDriver: true,
            }).start();

            setTimeout(() => setIsAnimating(false), 500); // débloque à la fin
        } else {
            setIsAnimating(true);
            Animated.spring(scaleAnim, {
            toValue: 0.8,
            friction: 6,
            tension: 80,
            useNativeDriver: true,
            }).start();
            setTimeout(() => setIsAnimating(false), 500);
        }
    }, [currentCard]);


  const overlayOpacity = isActive
    ? swipeAnim.interpolate({
        inputRange: [-1, 0, 1],
        outputRange: [0.7, 0, 0.7],
        extrapolate: "clamp",
      })
    : 0;

  const overlayColor = isActive
    ? swipeAnim.interpolate({
        inputRange: [-1, 0, 1],
        outputRange: ["rgba(255,0,0,1)", "rgba(0,0,0,0)", "rgba(0,255,0,1)"],
        extrapolate: "clamp",
      })
    : "transparent";


  return (
    <Animated.View style={[styles.shadowContainer, { shadowRadius: style.shadow, transform: [{ scale: scaleAnim }] }]}>
      <Animated.View style={styles.cardContainer}>
        <Image source={{ uri: card.image }} style={styles.image} />
        {/* Gradient overlay pour lisibilité */}
        <View style={[styles.gradientOverlay]} />

        <Animated.View
        pointerEvents="none"
        style={{
            ...StyleSheet.absoluteFillObject,
            backgroundColor: overlayColor,
            opacity: overlayOpacity,
            borderRadius: 20,
        }}
        />

        {/* Infos texte */}
        <View style={styles.textContainer}>
            <Text style={styles.title}>{card.nom}</Text>
            <Text style={styles.subtitle}>
                {card.categorie} • {card.origine}
            </Text>
            <Text style={styles.ingredientsTitle}>Ingrédients:</Text>
            <View style={styles.bubblesContainer}>
                {card.ingredients.map((ing, idx) => (
                    idx < 9 ? (
                        <View key={idx} style={styles.bubble}>
                            <Text style={styles.bubbleText}>{ing.name}</Text>
                        </View>
                    ) : idx === 9 ? (
                        <View key={idx} style={styles.bubble}>
                            <Text style={styles.bubbleText}>...</Text>
                        </View>
                    ) : null
                ))}
            </View>
        </View>

        {/* Badge rareté */}
        <View style={[styles.rarityBadge, { backgroundColor: style.color }]}>
            <Text style={styles.rarityText}>{card.rarety.toUpperCase()}</Text>
        </View>
      </Animated.View>
    </Animated.View>
  );
};

const SwipeRecipes = () => {
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [currentCard, setCurrentCard] = useState(0);
  const [isFetching, setIsFetching] = useState(false);

    const swiperRef = useRef<any>(null); // ✅ ref pour contrôler le swiper


  const [compteur, setCompteur] = useState(100);

  const [isAnimating, setIsAnimating] = useState(false);
  const lastVibration = useRef(0);
  const [matchCard, setMatchCard] = useState<Recipe | null>(null);
    const [showMatch, setShowMatch] = useState(false);



  const swipeAnim = useRef(new Animated.Value(0)).current;

  const fetchRecipes = async () => {
    if (isFetching) return;
    setIsFetching(true);
    try {
      const fetchedRecipes: Recipe[] = [];
      for (let i = 0; i < 10; i++) {
        const res = await api.get("api/get-recipe/");
        fetchedRecipes.push(res.data);
      }
      setRecipes((prev) => [...prev, ...fetchedRecipes]);
    } catch (err) {
      Alert.alert("Erreur", "Impossible de récupérer les recettes" + err);
    } finally {
      setIsFetching(false);
    }
  };

  useEffect(() => {
    fetchRecipes();
  }, []);

  useEffect(() => {
    console.log("bloque ? ", isAnimating);

  }, [isAnimating]);

    const handleLike = () => {
        if (swiperRef.current && compteur > 0) {
        swiperRef.current.swipeRight();
        }
    };

    const handlePass = () => {
        if (swiperRef.current) {
        swiperRef.current.swipeLeft();
        }
    };

  const handleSwipeRight = async(cardIndex: number) => {
    if (compteur <= 0) {
        Alert.alert("Plus de likes !", "Vous n'avez plus de likes disponibles.");
        return;
    }
    const likedRecipe = recipes[cardIndex];
    
    // Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    const res = await api.post("api/like-recipe/", { recipe_id: likedRecipe.id });

    if (res.data.match) {
        setMatchCard(likedRecipe);
        setShowMatch(true);
        console.log("Match ! ", likedRecipe);
        Vibration.vibrate();
        setTimeout(() => setShowMatch(false), 1500);

    }
  };

  const handleSwipeLeft = (cardIndex: number) => {
    console.log("Recette passée :", recipes[cardIndex].nom);
  };

  const handleOnSwiped = (cardIndex: number) => {
    swipeAnim.setValue(0); 
    setCurrentCard(cardIndex + 1);
    setCompteur(compteur - 1);
    if (recipes.length - cardIndex <= 3) fetchRecipes();
  };

  if (recipes.length === 0) {
    return (
      <View style={styles.center}>
        <Text style={{ fontSize: 18, fontWeight: "600", color: "#555" }}>
          Chargement des recettes...
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Swiper
        ref={swiperRef}
        cards={recipes}
        cardIndex={0}
        stackSize={2}
        stackSeparation={0}
        disableTopSwipe={isAnimating}
        disableBottomSwipe={true}
        disableLeftSwipe={isAnimating}
        disableRightSwipe={isAnimating || compteur <= 0}
        verticalSwipe={!isAnimating}
        horizontalSwipe={!isAnimating} // espace entre les cartes superposées
        stackScale={0.4}
        animateOverlayLabelsOpacity={true}
        onSwipedRight={handleSwipeRight}
        onSwipedLeft={handleSwipeLeft}
        onSwiped={handleOnSwiped}
        onSwiping={(x) => {
          Animated.timing(swipeAnim, {
            toValue: Math.max(Math.min(x / 150, 1), -1),
            duration: 5,
            useNativeDriver: true,
          }).start();
          const now = Date.now();
          if (x > 100 && compteur <= 0) {
                Alert.alert("Plus de likes !", "Vous n'avez plus de likes disponibles.");
                return;
          }
            if (x > 50 && now - lastVibration.current > 150) {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                lastVibration.current = now;
            }
        }}
        onSwipedAborted={() => {
            // Reset l'animation à zéro quand l'utilisateur relâche la carte
            Animated.spring(swipeAnim, {
            toValue: 0,
            friction: 6,
            tension: 80,
            useNativeDriver: true,
            }).start();
        }}
        renderCard={(card, index) => (
            <RecipeCard
                card={card}
                isActive={index === currentCard}
                swipeAnim={swipeAnim}
                setIsAnimating={setIsAnimating}
                currentCard={currentCard}
            />
        )}
      />
      <Modal
            visible={showMatch && !!matchCard}
            transparent
            animationType="fade"
            >
            <View style={styles.matchOverlayContainer}>
                <Animated.View style={styles.matchCardContainer}>
                <Image source={{ uri: matchCard?.image }} style={styles.matchImage} />
                <Text style={styles.matchText}>MATCH !</Text>
                </Animated.View>
            </View>
        </Modal>
    </View>
    
  );
};

const { width, height } = Dimensions.get("window");

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    position: "relative",
  },
  shadowContainer: {
    width: width * 0.9,
    height: height * 0.75,
    alignItems: "center",
    justifyContent: "center",
    // borderRadius: 20,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 1,
    elevation: 8,
    backgroundColor: "transparent",
  },
  cardContainer: {
    width: width * 0.9,
    height: height * 0.75,
    transform: [{ scale: 1 }],
    borderRadius: 20,
    // borderWidth: 4,
    overflow: "hidden",
    marginVertical: 10,
    backgroundColor: "#191619",
  },
  image: {
    width: "100%",
    height: "60%",
    position: "absolute",
    zIndex: 1,
  },
  gradientOverlay: {
    position: "absolute",
    bottom: 0,
    height: "40%",
    width: "100%",
    backgroundColor: "rgba(0,0,0,0.4)",
  },
  iconContainer: {
    position: "absolute",
    top: "40%",
  },
  textContainer: {
    position: "absolute",
    bottom: 25,
    left: 20,
    right: 20,
  },
  title: {
    fontSize: 26,
    fontWeight: "bold",
    color: "#fff",
    textShadowColor: "rgba(0,0,0,0.6)",
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  subtitle: {
    fontSize: 16,
    color: "#ddd",
    marginTop: 6,
  },
  rarityBadge: {
    position: "absolute",
    top: 15,
    right: 15,
    zIndex: 999,
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 12,
    opacity: 0.9,
  },
  rarityText: {
    color: "#fff",
    fontWeight: "700",
    fontSize: 12,
  },
  center: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  labelContainer: {
  position: "absolute",
  zIndex: 10,
},

labelText: {
  fontSize: 48,
  fontWeight: "800",
  textShadowColor: "rgba(0,0,0,0.3)",
  textShadowOffset: { width: 2, height: 2 },
  textShadowRadius: 4,
},
matchOverlayContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0,0,0,0.6)",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 999,
  },
  matchCardContainer: {
    width: "80%",
    height: "60%",
    borderRadius: 20,
    overflow: "hidden",
    justifyContent: "center",
    alignItems: "center",
    transform: [{ rotate: "-10deg" }],
  },
  matchImage: {
    width: "100%",
    height: "100%",
    borderRadius: 20,
  },
  matchText: {
    position: "absolute",
    top: 20,
    fontSize: 48,
    fontWeight: "900",
    color: "#FF2D55",
    textShadowColor: "rgba(0,0,0,0.6)",
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 8,
  },

  ingredientsTitle: {
  marginTop: 10,
  fontWeight: "700",
  fontSize: 16,
  color: "#fff",
},
ingredientText: {
  fontSize: 14,
  color: "#eee",
},
bubblesContainer: {
  flexDirection: "row",
  flexWrap: "wrap",
  marginTop: 5,
},
bubble: {
  backgroundColor: "rgba(255,255,255,0.2)",
  paddingHorizontal: 8,
  paddingVertical: 4,
  borderRadius: 15,
  margin: 3,
},
bubbleText: {
  fontSize: 12,
  color: "#fff",
},
// buttonsRow: {
//   flexDirection: "row",
//   justifyContent: "space-around",
//   alignItems: "center",
//   width: "80%",
//   position: "absolute",
//   bottom: 40,
// },
// actionButton: {
//   width: 70,
//   height: 70,
//   borderRadius: 35,
//   justifyContent: "center",
//   alignItems: "center",
//   shadowColor: "#000",
//   shadowOpacity: 0.2,
//   shadowRadius: 6,
//   elevation: 5,
// },
});

export default SwipeRecipes;
