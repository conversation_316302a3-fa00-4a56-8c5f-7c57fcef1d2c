import random
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated, AllowAny
import requests

from recipe.models import Ingredient, Recipe, RecipeIngredient, RecipeInstruction, RecipeMatched

class CreateMealDBRecette(APIView):
    """
    API endpoint pour créer une recette depuis TheMealDB.
    """
    permission_classes = [AllowAny]
    MEALDB_URL = "https://www.themealdb.com/api/json/v1/1/random.php"


    def get(self, request):
        """
        GET: Récupère une recette aléatoire depuis TheMealDB et la crée en base de données.
        """
        for _ in range(100):
            meal = self.get_mealdb_recipe()

            # print(meal)

            def assign_rarity(meal):
                # Rajoute une categorie aléatoire (commun, rare, legendaire, unique)
                RARETIES = [
                    {"name": "commun", "color": "#A0A0A0", "weight": 60},      # 60%
                    {"name": "rare", "color": "#3B82F6", "weight": 25},        # 25%
                    {"name": "legendaire", "color": "#9333EA", "weight": 10},  # 10%
                    {"name": "unique", "color": "#FACC15", "weight": 2},       # 2%
                ]
                rarety = random.choices(
                    RARETIES,
                    weights=[r["weight"] for r in RARETIES],
                    k=1
                )[0]
                meal["rarety"] = rarety["name"]
                meal["rarety_color"] = rarety["color"]
                return meal

            meal = assign_rarity(meal)

            ingredients = []
            quantities = []

            for i in range(1, 21):
                ingredient = meal.get(f"strIngredient{i}")
                if ingredient:
                    ingredients.append(ingredient)
                    quantities.append(meal.get(f"strMeasure{i}"))

            instructions = meal.get("strInstructions")
            
            recipe = Recipe.objects.get_or_create(
                name=meal.get("strMeal"),
                category=meal.get("strCategory"),
                origin=meal.get("strArea"),
                image=meal.get("strMealThumb"),
                rarety=meal.get("rarety"),
                # ingredients=ingredients
            )

            RecipeInstruction.objects.get_or_create(
                recipe=recipe[0],
                instruction=instructions
            )


            for ingredient in ingredients:
                ing = Ingredient.objects.get_or_create(
                    name=ingredient,
                )

                RecipeIngredient.objects.get_or_create(
                    recipe=recipe[0],
                    quantity=1,
                    ingredient=ing[0]
                )

    def get_mealdb_recipe(self):
        """
        Récupère une recette aléatoire depuis TheMealDB.
        """
        try:
            response = requests.get(self.MEALDB_URL, timeout=5)
            response.raise_for_status()  # Lève une exception si le status n'est pas 200
        except requests.RequestException:
            return Response(
                {"error": "Impossible de récupérer la recette depuis l'API externe."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        data = response.json()
        meal = data.get("meals", [])

        if not meal:
            return Response(
                {"error": "Aucune recette trouvée."},
                status=status.HTTP_404_NOT_FOUND
            )

        meal = meal[0]
        return meal


class GetRecipe(APIView):
    """
    API endpoint pour récupérer une recette aléatoire depuis TheMealDB.
    """

    permission_classes = [IsAuthenticated]


    def get(self, request):
        """
        GET: Récupère une recette aléatoire et retourne les informations principales.
        """

        # get random recipe 
        recipe = random.choice(Recipe.objects.all())
        recette = {
            "id": recipe.id,
            "nom": recipe.name,
            "categorie": recipe.category,
            "origine": recipe.origin,
            "image": recipe.image,
            "rarety": recipe.rarety,
            "ingredients": [
                {
                    "name": ingredient.ingredient.name,
                    "quantite": ingredient.quantity
                }
                for ingredient in recipe.ingredients.all()
            ]
        }

        return Response(recette, status=status.HTTP_200_OK)


class LikeRecipe(APIView):
    """
    API endpoint pour liker une recette.
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        recipe_id = request.data.get("recipe_id")

        # 10% de chance de match
        is_match = random.random() < 0.2

        if is_match:
            RecipeMatched.objects.create(
                user=request.user,
                recipe=Recipe.objects.get(id=recipe_id)
            )

        return Response(
            {
                "message": "Recette likée avec succès.",
                "match": is_match
            },
            status=status.HTTP_200_OK
        )
    
class GetMatchedRecipes(APIView):
    """
    API endpoint pour récupérer les recettes matchées.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        recipes = RecipeMatched.objects.filter(user=request.user)
        return Response(
            {
                "recipes": [
                    {
                        "id": recipe.recipe.id,
                        "nom": recipe.recipe.name,
                        "categorie": recipe.recipe.category,
                        "origine": recipe.recipe.origin,
                        "image": recipe.recipe.image,
                        "rarety": recipe.recipe.rarety,
                    }
                    for recipe in recipes
                ]
            },
            status=status.HTTP_200_OK
        )
    
class GetRecipeDetails(APIView):
    """
    API endpoint pour récupérer les détails d'une recette.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, recipe_id):
        print(recipe_id)
        recipe = Recipe.objects.get(id=recipe_id)
        return Response(
            {
                "id": recipe.id,
                "nom": recipe.name,
                "categorie": recipe.category,
                "origine": recipe.origin,
                "image": recipe.image,
                "rarety": recipe.rarety,
                "ingredients": [
                    {
                        "name": ingredient.ingredient.name,
                        "quantite": ingredient.quantity
                    }
                    for ingredient in recipe.ingredients.all()
                ],
                "instructions": recipe.instructions.first().instruction
            },
            status=status.HTTP_200_OK
        )