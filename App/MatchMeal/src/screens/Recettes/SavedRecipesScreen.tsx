import React, { useCallback, useEffect, useState } from "react";
import {
  View,
  Text,
  FlatList,
  Image,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import api from "../../api/api";
import { useFocusEffect } from "@react-navigation/native";

interface Recipe {
  id: number;
  nom: string;
  categorie: string;
  origine: string;
  image: string;
  rarety: string;
}

const raretyColors: Record<string, string> = {
    légendaire: "#E67E22",
    épique: "#9B59B6",
    rare: "#4A90E2",
    commun: "#aaa",
};

const SavedRecipesScreen = ({ navigation }: any) => {
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [search, setSearch] = useState("");
  const [filter, setFilter] = useState<string | null>(null);

  const fetchSavedRecipes = async () => {
    try {
      const res = await api.get("api/get-matched-recipes/");
      const mapped = res.data.recipes.map((item: any, index: number) => ({
        id: item.id || index,
        nom: item.nom || item.strMeal,
        categorie: item.categorie || item.strCategory,
        origine: item.origine || item.strArea,
        image: item.image || item.strMealThumb,
        rarety: item.rarety || "commun",
      }));
      setRecipes(mapped);
    } catch (err) {
      console.error("Erreur récupération favoris :", err);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchSavedRecipes();
    }, [])
  );

  // filtrage + recherche
  const filteredRecipes = recipes.filter((r) => {
    const matchSearch = r.nom.toLowerCase().includes(search.toLowerCase());
    const matchFilter = filter ? r.rarety === filter : true;
    return matchSearch && matchFilter;
  });

  // groupement par rareté
  const groupedByRarety = filteredRecipes.reduce((acc: any, recipe) => {
    if (!acc[recipe.rarety]) acc[recipe.rarety] = [];
    acc[recipe.rarety].push(recipe);
    return acc;
  }, {});

  const renderRecipe = ({ item }: { item: Recipe }) => (
    <TouchableOpacity
      style={styles.card}
      onPress={() => navigation.navigate("RecipeDetails", { recipe_id: item.id })}
    >
      <Image source={{ uri: item.image }} style={styles.image} />
      <View style={styles.info}>
        <Text style={styles.title}>{item.nom}</Text>
        <Text style={styles.subtitle}>
          {item.categorie} • {item.origine}
        </Text>
      </View>

      {/* Badge rareté */}
      <View
        style={[
          styles.badge,
          { backgroundColor: raretyColors[item.rarety] || "#555" },
        ]}
      >
        <Text style={styles.badgeText}>{item.rarety}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Mes matchs</Text>
      </View>

      {/* Recherche */}
      <TextInput
        style={styles.searchInput}
        placeholder="Rechercher une recette..."
        value={search}
        onChangeText={setSearch}
      />

      {/* Filtres */}
      <View style={styles.filters}>
        {["commun", "rare", "épique", "légendaire"].map((r) => (
          <TouchableOpacity
            key={r}
            style={[
              styles.filterBtn,
              filter === r && { backgroundColor: raretyColors[r] },
            ]}
            onPress={() => setFilter(filter === r ? null : r)}
          >
            <Text style={[styles.filterText, filter === r && { color: "#fff" }]}>
              {r}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Liste groupée */}
      <FlatList
        data={Object.keys(groupedByRarety)}
        keyExtractor={(rarety) => rarety}
        renderItem={({ item: rarety }) => (
          <View key={rarety}>
            <Text style={styles.sectionTitle}>{rarety.toUpperCase()}</Text>
            {groupedByRarety[rarety].map((recipe: Recipe) =>
              renderRecipe({ item: recipe })
            )}
          </View>
        )}
        contentContainerStyle={{ padding: 20 }}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  header: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  headerTitle: { fontSize: 22, fontWeight: "700", color: "#222" },
  card: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fafafa",
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  image: { width: 70, height: 70, borderRadius: 10, marginRight: 12 },
  info: { flex: 1 },
  title: { fontSize: 16, fontWeight: "600", color: "#333" },
  subtitle: { fontSize: 14, color: "#666", marginTop: 4 },
  badge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 8,
  },
  badgeText: { color: "#fff", fontWeight: "700", fontSize: 12 },
  searchInput: {
    margin: 15,
    padding: 10,
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 10,
  },
  filters: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginBottom: 10,
  },
  filterBtn: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: "#f0f0f0",
  },
  filterText: { fontSize: 14, color: "#333" },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "700",
    marginVertical: 10,
    color: "#444",
  },
});

export default SavedRecipesScreen;