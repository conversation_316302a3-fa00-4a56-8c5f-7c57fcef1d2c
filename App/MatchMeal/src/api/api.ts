import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

const API_URL = 'http://192.168.1.32:8000/'; // adresse de ton backend Django

const api = axios.create({
  baseURL: API_URL,
});

// Ajouter JWT automatiquement aux requêtes
api.interceptors.request.use(async config => {
    if (!config.url?.includes('login') && !config.url?.includes('register')) {
        const token = await AsyncStorage.getItem('accessToken');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
            config.headers['Content-Type'] = 'application/json';
        }
        return config;
    }
    config.headers['Content-Type'] = 'application/json';
    return config;
});

export default api;
